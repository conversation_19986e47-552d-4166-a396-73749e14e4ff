import pandas as pd
import numpy as np
from datetime import datetime
import os

# Caminhos dos arquivos
arquivo_cliente = "/mnt/data/cliente.xlsx"
arquivo_modelo = "/mnt/data/modelo.csv"
arquivo_divergencias = "/mnt/data/Divergências BI Platform (2).xlsx"

# Leitura dos arquivos
df_cliente = pd.read_excel(arquivo_cliente)
df_modelo = pd.read_csv(arquivo_modelo)
df_div = pd.read_excel(arquivo_divergencias)

# Criação do dicionário de mapeamento Platform → BI
mapa = dict(zip(df_div['PLATFORM'], df_div['BI']))

# Renomear colunas da planilha cliente com base no mapeamento
df_cliente.rename(columns=mapa, inplace=True)

# Lista de colunas finais (ordem da planilha modelo)
colunas_finais = df_modelo.columns.tolist()

# Garantir que todas as colunas estejam presentes
for col in colunas_finais:
    if col not in df_cliente.columns:
        df_cliente[col] = ""

# --- Regras de formatação específicas ---

# 3. Separar Data e Hora da Infração (coluna "Data da Infração" e "Hora da Infração")
def separar_data_hora(valor):
    if pd.isnull(valor):
        return "", ""
    try:
        dt = pd.to_datetime(valor)
        return dt.strftime('%Y-%m-%d'), dt.strftime('%H:%M:%S')
    except:
        return "", ""

df_cliente['Data da Infração'], df_cliente['Hora da Infração'] = zip(*df_cliente['Data da Infração'].apply(separar_data_hora))

# 4. Colunas com formato "aaaa-mm-dd"
df_cliente['Data da Infração'] = pd.to_datetime(df_cliente['Data da Infração'], errors='coerce').dt.strftime('%Y-%m-%d')

# 5. Datas com "aaaa-mm-dd hh:mm:ss" ou vazio
colunas_data_hora = ['Data Vencimento Original', 'Data Pagamento', 'Data Vencimento Boleto', 
                     'Data Limite Recurso', 'Data Limite IC', 'Conhecido Em', 'Última Captura']

for col in colunas_data_hora:
    df_cliente[col] = pd.to_datetime(df_cliente[col], errors='coerce')
    df_cliente[col] = df_cliente[col].apply(lambda x: x.strftime('%Y-%m-%d %H:%M:%S') if not pd.isnull(x) else "")

# 6. Valor e Valor Pago formatados como "R$ #.##0,00"
def formatar_valor(valor):
    try:
        v = round(float(valor), 2)
        return f"R$ {v:,.2f}".replace(",", "v").replace(".", ",").replace("v", ".")
    except:
        return "R$ 00,00"

df_cliente['Valor'] = df_cliente['Valor'].apply(formatar_valor)
df_cliente['Valor Pago'] = df_cliente['Valor Pago'].apply(formatar_valor)

# 7. Endereço = CONCAT(Cidade - Estado / UF)
if all(col in df_cliente.columns for col in ['Cidade', 'Estado', 'UF']):
    df_cliente['Endereço'] = df_cliente.apply(
        lambda row: f"{row['Cidade']} - {row['Estado']} / {row['UF']}" if not pd.isnull(row['Cidade']) else "",
        axis=1
    )

# 8. Reordenar colunas para seguir padrão da planilha modelo
df_saida = df_cliente[colunas_finais]

# 9. Exportar CSV codificado em UTF-8
saida_csv = "/mnt/data/saida_final.csv"
df_saida.to_csv(saida_csv, index=False, encoding='utf-8')

# 10. Exportar também para XLS
saida_xls = "/mnt/data/saida_final.xlsx"
df_saida.to_excel(saida_xls, index=False)

print("Arquivos exportados com sucesso.")