import pandas as pd
from datetime import datetime
import os

# ====================
# Funções auxiliares
# ====================
def formatar_data(data):
    try:
        return pd.to_datetime(data).strftime('%d/%m/%Y')
    except:
        return ''

def formatar_hora(data):
    try:
        return pd.to_datetime(data).strftime('%H:%M')
    except:
        return ''

def formatar_data_hora(data):
    try:
        return pd.to_datetime(data).strftime('%d/%m/%Y %H:%M')
    except:
        return ''

def formatar_valor(valor):
    try:
        valor = float(valor)
        return f'R$ {valor:,.2f}'.replace(',', 'v').replace('.', ',').replace('v', '.')
    except:
        return 'R$ 0,00'

# =========================
# Automação APISUL (1)
# =========================
def processar_apisul():
    print("\n🔄 Processando automação para APISUL...")

    cliente_df = pd.read_excel("cliente.xlsx")
    mapeamento_df = pd.read_excel("Divergências BI Platform (2).xlsx")
    mapeamento = dict(zip(mapeamento_df["PLATFORM"].fillna(""), mapeamento_df["BI"].fillna("")))
    mapeamento = {k: v for k, v in mapeamento.items() if v and v.lower() != 'não tem'}

    cliente_df.rename(columns=mapeamento, inplace=True)

    # Modelo de colunas do resultado
    colunas_finais = [
        'CNPJ', 'Placa', 'Renavam', 'Auto de Infração', 'Renainf', 'Situação',
        'Situação Original', 'Data da Infração', 'Hora da Infração',
        'Data Vencimento Original', 'Data Pagamento', 'Valor', 'Valor Pago',
        'Classe', 'Subclasse', 'Pontos da Infração', 'Tipo Multa', 'Auto de Infração Original',
        'Orgão Consultado', 'Orgão Autuador', 'Código', 'Descrição', 'Descrição Orgão Consultado',
        'Endereço', 'Data Vencimento Boleto', 'Atenção', 'Guia de Pagamento',
        'Linha Digitável', 'Indicação de Condutor (IC)', 'Demonstrativo AIT', 'Outros Documentos',
        'Data Limite Recurso', 'Data Limite IC', 'Conhecido Em', 'Última Captura',
        'Lista AITs e Renainfs', 'Tags', 'Metadado Multa', 'Metadado Veículo (1)',
        'Metadado Veículo (2)', 'Metadado Veículo (3)', 'Metadado Veículo (4)',
        'Metadado Veículo Consolidado', 'id'
    ]

    for col in colunas_finais:
        if col not in cliente_df.columns:
            cliente_df[col] = ''

    cliente_df['Data da Infração'] = cliente_df['Data da Infração'].apply(formatar_data)
    cliente_df['Hora da Infração'] = cliente_df['Data da Infração'].apply(formatar_hora)
    
    for col in ['Data Vencimento Original', 'Data Pagamento', 'Data Vencimento Boleto',
                'Data Limite Recurso', 'Data Limite IC', 'Conhecido Em', 'Última Captura']:
        cliente_df[col] = cliente_df[col].apply(formatar_data_hora)

    for col in ['Valor', 'Valor Pago']:
        cliente_df[col] = cliente_df[col].apply(formatar_valor)

    if {'Cidade', 'UF', 'Código da cidade'}.issubset(cliente_df.columns):
        cliente_df['Endereço'] = cliente_df['Cidade'].fillna('') + ' - ' + cliente_df['UF'].fillna('') + ' / ' + cliente_df['Código da cidade'].fillna('')
        cliente_df['Endereço'] = cliente_df['Endereço'].str.replace(' -  / ', '')

    resultado = cliente_df[colunas_finais]
    resultado.to_csv("resultado_final.csv", index=False, encoding="utf-8")
    resultado.to_excel("resultado_final.xlsx", index=False)

    print("✅ Arquivos 'resultado_final.csv' e 'resultado_final.xlsx' gerados com sucesso.")

# =============================
# Automação Outros Clientes (2)
# =============================
def processar_outros_clientes():
    print("\n🔄 Processando automação para Outros Clientes...")

    cliente_df = pd.read_excel("cliente.xlsx")
    editavel_df = pd.read_excel("Arquivo editável.xlsx")

    mapeamento_df = pd.read_excel("Divergências BI Platform (2).xlsx")
    mapeamento = dict(zip(mapeamento_df["PLATFORM"].fillna(""), mapeamento_df["BI"].fillna("")))
    mapeamento = {k: v for k, v in mapeamento.items() if v and v.lower() != 'não tem'}

    cliente_df.rename(columns=mapeamento, inplace=True)

    for col in editavel_df.columns:
        if col in cliente_df.columns:
            editavel_df[col] = cliente_df[col]
        else:
            editavel_df[col] = ''

    if {'Cidade', 'UF', 'Código da cidade'}.issubset(cliente_df.columns):
        editavel_df['Endereço'] = cliente_df['Cidade'].fillna('') + ' - ' + cliente_df['UF'].fillna('') + ' / ' + cliente_df['Código da cidade'].fillna('')
        editavel_df['Endereço'] = editavel_df['Endereço'].str.replace(' -  / ', '')

    if {'UF', 'Cidade'}.issubset(cliente_df.columns):
        editavel_df['Município / UF'] = cliente_df['Cidade'].fillna('') + ' / ' + cliente_df['UF'].fillna('')

    editavel_df['Data da Infração'] = cliente_df['Data da Infração'].apply(formatar_data)
    editavel_df['Hora da Infração'] = cliente_df['Data da Infração'].apply(formatar_hora)

    editavel_df.to_excel("outros_clientes_final.xlsx", index=False)
    print("✅ Arquivo 'outros_clientes_final.xlsx' gerado com sucesso.")

# ====================
# Menu principal
# ====================
def menu():
    while True:
        print("\n=== MENU DE AUTOMAÇÃO ===")
        print("1. APISUL")
        print("2. Outros Clientes")
        print("3. Sair")
        opcao = input("Escolha uma opção (1, 2 ou 3): ")

        if opcao == "1":
            processar_apisul()
        elif opcao == "2":
            processar_outros_clientes()
        elif opcao == "3":
            print("Programa encerrado.")
            break
        else:
            print("❌ Opção inválida.")

        repetir = input("\nDeseja executar novamente? (1 - Sim / 2 - Não): ")
        if repetir != "1":
            print("Encerrando...")
            break

# Executar menu
if __name__ == "__main__":
    menu()