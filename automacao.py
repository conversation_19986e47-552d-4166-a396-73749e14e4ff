import pandas as pd
from openpyxl import Workbook
from datetime import datetime
import numpy as np

# Arquivos de entrada
caminho_entrada = 'cliente.xlsx'
caminho_mapeamento = 'Divergências BI Platform (2).xlsx'

# Arquivos de saída
csv_saida = 'resultado_final.csv'
xls_saida = 'resultado_final.xlsx'

# Leitura do mapeamento
mapeamento_df = pd.read_excel(caminho_mapeamento, header=0)
mapeamento_dict = dict(zip(mapeamento_df['PLATFORM'].fillna(''), mapeamento_df['BI'].fillna('')))

# Remove colunas sem correspondência no BI
mapeamento_dict = {k: v for k, v in mapeamento_dict.items() if v and v.lower() != 'não tem'}

# Leitura da planilha do cliente
df = pd.read_excel(caminho_entrada)

# Renomeia colunas com base no mapeamento
df.rename(columns=mapeamento_dict, inplace=True)

# Cria colunas padrão para o modelo final
colunas_finais = [
    'CNPJ', 'Placa', 'Renavam', 'Auto de Infração', 'Renainf', 'Situação',
    'Situação Original', 'Data da Infração', 'Hora da Infração',
    'Data Vencimento Original', 'Data Pagamento', 'Valor', 'Valor Pago',
    'Classe', 'Subclasse', 'Pontos da Infração', 'Tipo Multa', 'Auto de Infração Original',
    'Orgão Consultado', 'Orgão Autuador', 'Código', 'Descrição', 'Descrição Orgão Consultado',
    'Endereço', 'Data Vencimento Boleto', 'Atenção', 'Guia de Pagamento',
    'Linha Digitável', 'Indicação de Condutor (IC)', 'Demonstrativo AIT', 'Outros Documentos',
    'Data Limite Recurso', 'Data Limite IC', 'Conhecido Em', 'Última Captura',
    'Lista AITs e Renainfs', 'Tags', 'Metadado Multa', 'Metadado Veículo (1)',
    'Metadado Veículo (2)', 'Metadado Veículo (3)', 'Metadado Veículo (4)',
    'Metadado Veículo Consolidado', 'id'
]

# Adiciona colunas faltantes com valor vazio
for col in colunas_finais:
    if col not in df.columns:
        df[col] = ''

# Normaliza datas e horários
def formatar_data(data):
    if pd.isna(data):
        return ''
    try:
        return pd.to_datetime(data).strftime('%d/%m/%Y')
    except:
        return ''

def formatar_hora(data):
    if pd.isna(data):
        return ''
    try:
        return pd.to_datetime(data).strftime('%H:%M')
    except:
        return ''

def formatar_data_hora(data):
    if pd.isna(data):
        return ''
    try:
        return pd.to_datetime(data).strftime('%d/%m/%Y %H:%M')
    except:
        return ''

def formatar_valor(valor):
    try:
        valor = float(valor)
        return f'R$ {valor:,.2f}'.replace(',', 'v').replace('.', ',').replace('v', '.')
    except:
        return 'R$ 0,00'

# Colunas de data/hora específicas
df['Data da Infração'] = df['Data da Infração'].apply(formatar_data)
df['Hora da Infração'] = df['Hora da Infração'].apply(formatar_hora)
df['Data Vencimento Original'] = df['Data Vencimento Original'].apply(formatar_data_hora)
df['Data Pagamento'] = df['Data Pagamento'].apply(formatar_data_hora)
df['Data Vencimento Boleto'] = df['Data Vencimento Boleto'].apply(formatar_data_hora)
df['Data Limite Recurso'] = df['Data Limite Recurso'].apply(formatar_data_hora)
df['Data Limite IC'] = df['Data Limite IC'].apply(formatar_data_hora)
df['Conhecido Em'] = df['Conhecido Em'].apply(formatar_data_hora)
df['Última Captura'] = df['Última Captura'].apply(formatar_data_hora)

# Formata valores monetários
df['Valor'] = df['Valor'].apply(formatar_valor)
df['Valor Pago'] = df['Valor Pago'].apply(formatar_valor)

# Concatena endereço se houver dados de Cidade, UF e Código
if {'Cidade', 'UF', 'Código da cidade'}.issubset(df.columns):
    df['Endereço'] = df['Cidade'].fillna('') + ' - ' + df['UF'].fillna('') + ' / ' + df['Código da cidade'].fillna('')
    df['Endereço'] = df['Endereço'].str.replace(' -  / ', '')  # Limpa se algum campo estiver vazio

# Reordena colunas
df_final = df[colunas_finais]

# Exporta CSV com encoding UTF-8
df_final.to_csv(csv_saida, index=False, sep=',', encoding='utf-8')

# Exporta XLSX
df_final.to_excel(xls_saida, index=False)

print("Transformação concluída com sucesso.")