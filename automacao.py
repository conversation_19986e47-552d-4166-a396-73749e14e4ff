import pandas as pd
from datetime import datetime

# ======= Funções auxiliares =======

def formatar_data(data):
    try:
        return pd.to_datetime(data).strftime('%d/%m/%Y')
    except:
        return ''

def formatar_hora(data):
    try:
        return pd.to_datetime(data).strftime('%H:%M')
    except:
        return ''

def formatar_data_hora(data):
    try:
        return pd.to_datetime(data).strftime('%d/%m/%Y %H:%M')
    except:
        return ''

def formatar_valor(valor):
    try:
        valor = float(valor)
        return f'R$ {valor:,.2f}'.replace(',', 'v').replace('.', ',').replace('v', '.')
    except:
        return 'R$ 0,00'

# ======= Colunas padrão =======

COLUNAS_FINAIS = [
    'CNPJ', 'Placa', 'Renavam', 'Auto de Infração', 'Renainf', 'Situação',
    'Situação Original', 'Data da Infração', 'Hora da Infração',
    'Data Vencimento Original', 'Data Pagamento', 'Valor', 'Valor Pago',
    'Classe', 'Subclasse', 'Pontos da Infração', 'Tipo Multa', 'Auto de Infração Original',
    'Orgão Consultado', 'Orgão Autuador', 'Código', 'Descrição', 'Descrição Orgão Consultado',
    'Endereço', 'Município', 'Data Vencimento Boleto', 'Atenção', 'Guia de Pagamento',
    'Linha Digitável', 'Indicação de Condutor (IC)', 'Demonstrativo AIT', 'Outros Documentos',
    'Data Limite Recurso', 'Data Limite IC', 'Conhecido Em', 'Última Captura',
    'Lista AITs e Renainfs', 'Tags', 'Metadado Multa', 'Metadado Veículo (1)',
    'Metadado Veículo (2)', 'Metadado Veículo (3)', 'Metadado Veículo (4)',
    'Metadado Veículo Consolidado', 'id'
]

# ======= Automação APISUL (1) =======

def processar_apisul():
    print("\n🔄 Processando automação para APISUL...")

    df = pd.read_excel("cliente.xlsx")
    mapa = pd.read_excel("Divergências BI Platform (2).xlsx")

    mapeamento = dict(zip(mapa["PLATFORM"].fillna(""), mapa["BI"].fillna("")))
    mapeamento = {k: v for k, v in mapeamento.items() if v and v.lower() != 'não tem'}

    df.rename(columns=mapeamento, inplace=True)

    for col in COLUNAS_FINAIS:
        if col not in df.columns:
            df[col] = ''

    df['Data da Infração'] = df['Data da Infração'].apply(formatar_data)
    df['Hora da Infração'] = df['Data da Infração'].apply(formatar_hora)

    for col in ['Data Vencimento Original', 'Data Pagamento', 'Data Vencimento Boleto',
                'Data Limite Recurso', 'Data Limite IC', 'Conhecido Em', 'Última Captura']:
        df[col] = df[col].apply(formatar_data_hora)

    for col in ['Valor', 'Valor Pago']:
        df[col] = df[col].apply(formatar_valor)

    if {'Cidade', 'UF', 'Código da cidade'}.issubset(df.columns):
        df['Endereço'] = (
            df['Cidade'].fillna('') + ' - ' +
            df['UF'].fillna('') + ' / ' +
            df['Código da cidade'].fillna('')
        ).str.replace(' -  / ', '')

    df_final = df[COLUNAS_FINAIS]
    df_final.to_csv("resultado_final_apisul.csv", index=False, encoding="utf-8")
    df_final.to_excel("resultado_final_apisul.xlsx", index=False)

    print("✅ Arquivos gerados: resultado_final_apisul.csv e .xlsx")

# ======= Automação Outros Clientes (2) =======

def processar_outros_clientes():
    print("\n🔄 Processando automação para Outros Clientes...")

    df = pd.read_excel("cliente.xlsx")
    mapa = pd.read_excel("Divergências BI Platform (2).xlsx")

    mapeamento = dict(zip(mapa["PLATFORM"].fillna(""), mapa["BI"].fillna("")))
    mapeamento = {k: v for k, v in mapeamento.items() if v and v.lower() != 'não tem'}

    df.rename(columns=mapeamento, inplace=True)

    for col in COLUNAS_FINAIS:
        if col not in df.columns:
            df[col] = ''

    df['Hora da Infração'] = df['Data da Infração'].apply(formatar_hora)
    df['Data da Infração'] = df['Data da Infração'].apply(formatar_data)

    # Município = Cidade / UF
    if 'Cidade' in df.columns and 'UF' in df.columns:
        df['Município'] = df['Cidade'].fillna('') + ' / ' + df['UF'].fillna('')
    else:
        df['Município'] = ''

    # Endereço = Endereço Original + " - " + Município
    if 'Endereço' in df.columns:
        df['Endereço'] = df['Endereço'].fillna('') + ' - ' + df['Município'].fillna('')
        df['Endereço'] = df['Endereço'].str.strip(' -')

    df_final = df[COLUNAS_FINAIS]
    df_final.to_csv("resultado_final_outros.csv", index=False, encoding="utf-8")
    df_final.to_excel("resultado_final_outros.xlsx", index=False)

    print("✅ Arquivos gerados: resultado_final_outros.csv e .xlsx")

# ======= Menu principal =======

def menu():
    while True:
        print("\n=== MENU DE AUTOMAÇÃO ===")
        print("1. APISUL")
        print("2. Outros Clientes")
        print("3. Sair")

        opcao = input("Escolha uma opção (1, 2 ou 3): ")

        if opcao == "1":
            processar_apisul()
        elif opcao == "2":
            processar_outros_clientes()
        elif opcao == "3":
            print("Encerrando o programa.")
            break
        else:
            print("❌ Opção inválida.")

        repetir = input("\nDeseja executar novamente? (1 - Sim / 2 - Não): ")
        if repetir != "1":
            print("Encerrando...")
            break

# ======= Executar =======
if __name__ == "__main__":
    menu()