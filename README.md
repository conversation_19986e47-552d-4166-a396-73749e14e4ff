# Automação de Processamento de Dados - API SUL

Este projeto automatiza o processamento e formatação de dados de multas e infrações, transformando planilhas do cliente em formato padronizado.

## 📋 Pré-requisitos

### Software Necessário
- Python 3.7 ou superior
- Bibliotecas Python:
  - `pandas`
  - `openpyxl`
  - `numpy`

### Instalação das Dependências
```bash
pip install pandas openpyxl numpy
```

## 📁 Estrutura de Arquivos

O projeto deve conter os seguintes arquivos na mesma pasta:

```
📂 Automação API SUL/
├── 📄 automacao.py          # Script principal
├── 📄 cliente.xlsx          # Dados do cliente (entrada)
├── 📄 modelo.csv            # Modelo de estrutura das colunas
├── 📄 Divergências BI Platform (2).xlsx  # Mapeamento de colunas
└── 📄 README.md            # Este arquivo de documentação
```

## 🔧 Arquivos de Entrada

### 1. cliente.xlsx
- **Descrição**: Planilha com os dados originais do cliente
- **Formato**: Excel (.xlsx)
- **Conteúdo**: Dados de multas e infrações no formato original

### 2. modelo.csv
- **Descrição**: Define a estrutura e ordem das colunas no arquivo final
- **Formato**: CSV com separador `;` e encoding `latin-1`
- **Função**: Serve como template para o formato de saída

### 3. Divergências BI Platform (2).xlsx
- **Descrição**: Tabela de mapeamento entre colunas
- **Colunas principais**:
  - `PLATFORM`: Nome da coluna no arquivo original
  - `BI`: Nome da coluna no formato padronizado
- **Função**: Converte nomes de colunas do formato original para o padrão

## 🚀 Como Usar

### Passo 1: Preparar os Arquivos
1. Coloque o arquivo `cliente.xlsx` com seus dados na pasta
2. Certifique-se de que os arquivos `modelo.csv` e `Divergências BI Platform (2).xlsx` estão presentes
3. Verifique se o script `automacao.py` está na mesma pasta

### Passo 2: Executar a Automação
Abra o terminal/prompt de comando na pasta do projeto e execute:

```bash
python automacao.py
```

### Passo 3: Verificar os Resultados
Após a execução bem-sucedida, serão gerados dois arquivos:
- `resultado_final.csv` - Arquivo CSV com encoding UTF-8
- `resultado_final.xlsx` - Arquivo Excel

## 📊 Transformações Aplicadas

### Formatação de Datas
- **Data da Infração**: Formato `dd/mm/aaaa`
- **Hora da Infração**: Formato `HH:MM`
- **Datas com horário**: Formato `dd/mm/aaaa HH:MM`
  - Data Vencimento Original
  - Data Pagamento
  - Data Vencimento Boleto
  - Data Limite Recurso
  - Data Limite IC
  - Conhecido Em
  - Última Captura

### Formatação de Valores
- **Valores monetários**: Formato `R$ #.###,##`
  - Valor
  - Valor Pago

### Formatação de Endereço
- **Concatenação**: `Cidade - UF / Código da cidade`
- Campos vazios são tratados automaticamente

### Estruturação de Colunas
- Adiciona colunas faltantes com valores vazios
- Reordena colunas conforme o modelo padrão
- Remove colunas não mapeadas

## ⚠️ Possíveis Problemas e Soluções

### Erro: "Permission denied"
**Problema**: Arquivo de saída está aberto no Excel
**Solução**: Feche o arquivo Excel antes de executar o script

### Erro: "No module named 'openpyxl'"
**Problema**: Biblioteca não instalada
**Solução**: Execute `pip install openpyxl`

### Erro: "UnicodeDecodeError"
**Problema**: Encoding incorreto do arquivo CSV
**Solução**: O script já trata isso automaticamente com `encoding='latin-1'`

### Avisos sobre formato de data
**Problema**: Warnings sobre parsing de datas
**Solução**: São apenas avisos informativos, não afetam o funcionamento

## 📈 Saída Esperada

Ao executar com sucesso, você verá:
```
Transformação concluída com sucesso.
```

E os arquivos de saída serão criados com:
- Dados formatados conforme as regras
- Colunas padronizadas e ordenadas
- Encoding correto (UTF-8 para CSV)

## 🔄 Fluxo de Processamento

1. **Leitura**: Carrega arquivos de entrada
2. **Mapeamento**: Aplica conversão de nomes de colunas
3. **Formatação**: Aplica regras de formatação específicas
4. **Estruturação**: Organiza colunas conforme modelo
5. **Exportação**: Gera arquivos CSV e Excel

## 📞 Suporte

Para problemas ou dúvidas:
1. Verifique se todos os arquivos estão na pasta correta
2. Confirme que as dependências estão instaladas
3. Certifique-se de que os arquivos não estão abertos em outros programas
4. Execute o script em um terminal com permissões adequadas

---

**Versão**: 1.0  
**Última atualização**: 2025-07-03
